#!/usr/bin/env node

/**
 * MinIO功能测试脚本
 * 测试页面中MinIO存储提供者的各项功能
 */

const http = require('http');

// 测试配置
const TEST_CONFIG = {
    host: 'localhost',
    port: 3000,
    timeout: 30000
};

// MinIO配置
const MINIO_CONFIG = {
    endpoint: 'http://127.0.0.1:9000',
    accessKeyId: 'FsYFOP9cOOYDyfM9odzX',
    secretAccessKey: 'AcQaaZTcoOh6GKMuLKAcdIo4W0qcWQ8VWKkqQvLl',
    bucketName: 'eversnip'
};

// 颜色输出
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

function colorize(text, color) {
    return `${colors[color]}${text}${colors.reset}`;
}

// HTTP请求函数
function makeRequest(path, method = 'GET', data = null) {
    return new Promise((resolve, reject) => {
        const options = {
            hostname: TEST_CONFIG.host,
            port: TEST_CONFIG.port,
            path: path,
            method: method,
            timeout: TEST_CONFIG.timeout,
            headers: {
                'Content-Type': 'application/json'
            }
        };

        const req = http.request(options, (res) => {
            let responseData = '';
            
            res.on('data', (chunk) => {
                responseData += chunk;
            });
            
            res.on('end', () => {
                resolve({
                    statusCode: res.statusCode,
                    headers: res.headers,
                    body: responseData
                });
            });
        });

        req.on('error', (err) => {
            reject(err);
        });

        req.on('timeout', () => {
            req.destroy();
            reject(new Error('Request timeout'));
        });

        if (data) {
            req.write(JSON.stringify(data));
        }

        req.end();
    });
}

// 检查MinIO服务器是否可访问
async function checkMinioServer() {
    console.log(colorize('🔍 检查MinIO服务器连接...', 'cyan'));
    
    try {
        const http = require('http');
        const url = require('url');
        
        const parsedUrl = url.parse(MINIO_CONFIG.endpoint);
        
        return new Promise((resolve, reject) => {
            const options = {
                hostname: parsedUrl.hostname,
                port: parsedUrl.port || 9000,
                path: '/minio/health/live',
                method: 'GET',
                timeout: 5000
            };

            const req = http.request(options, (res) => {
                resolve(res.statusCode === 200);
            });

            req.on('error', () => {
                resolve(false);
            });

            req.on('timeout', () => {
                req.destroy();
                resolve(false);
            });

            req.end();
        });
    } catch (error) {
        return false;
    }
}

// 测试页面配置验证
async function testPageConfiguration() {
    console.log(colorize('\n🧪 测试页面配置验证', 'bright'));
    
    try {
        const response = await makeRequest('/comprehensive-storage-test.html');
        
        if (response.statusCode !== 200) {
            throw new Error(`页面访问失败: ${response.statusCode}`);
        }
        
        // 检查配置是否正确嵌入
        const configChecks = [
            { name: 'Access Key', value: MINIO_CONFIG.accessKeyId },
            { name: 'Secret Key', value: MINIO_CONFIG.secretAccessKey },
            { name: 'Endpoint', value: MINIO_CONFIG.endpoint },
            { name: 'Bucket Name', value: MINIO_CONFIG.bucketName }
        ];
        
        const missingConfigs = [];
        for (const check of configChecks) {
            if (!response.body.includes(check.value)) {
                missingConfigs.push(check.name);
            }
        }
        
        if (missingConfigs.length > 0) {
            throw new Error(`配置缺失: ${missingConfigs.join(', ')}`);
        }
        
        console.log(colorize('✅ 页面配置验证通过', 'green'));
        console.log(colorize(`   📄 页面大小: ${response.body.length} 字符`, 'blue'));
        console.log(colorize(`   ⚙️ MinIO配置: 已正确嵌入`, 'blue'));
        
        return true;
        
    } catch (error) {
        console.log(colorize(`❌ 页面配置验证失败: ${error.message}`, 'red'));
        return false;
    }
}

// 测试页面功能组件
async function testPageComponents() {
    console.log(colorize('\n🧪 测试页面功能组件', 'bright'));
    
    try {
        const response = await makeRequest('/comprehensive-storage-test.html');
        
        const componentChecks = [
            'StorageProvider',
            'TestRunner', 
            'ComprehensiveStorageTest',
            'minio',
            'config-panel',
            'test-sections',
            'log-terminal'
        ];
        
        const missingComponents = [];
        for (const component of componentChecks) {
            if (!response.body.includes(component)) {
                missingComponents.push(component);
            }
        }
        
        if (missingComponents.length > 0) {
            throw new Error(`组件缺失: ${missingComponents.join(', ')}`);
        }
        
        console.log(colorize('✅ 页面组件验证通过', 'green'));
        console.log(colorize(`   🧩 检查组件: ${componentChecks.length} 个`, 'blue'));
        console.log(colorize(`   ✓ 所有组件: 都存在`, 'blue'));
        
        return true;
        
    } catch (error) {
        console.log(colorize(`❌ 页面组件验证失败: ${error.message}`, 'red'));
        return false;
    }
}

// 测试存储方法覆盖
async function testStorageMethods() {
    console.log(colorize('\n🧪 测试存储方法覆盖', 'bright'));
    
    try {
        const response = await makeRequest('/comprehensive-storage-test.html');
        
        const requiredMethods = [
            'initialize', 'dispose', 'testConnection',
            'get', 'put', 'delete', 'list',
            'getBatch', 'putBatch', 'deleteBatch',
            'getMetadata', 'getStream', 'putStream',
            'initiateMultipartUpload', 'uploadPart', 'completeMultipartUpload',
            'abortMultipartUpload', 'listMultipartUploads',
            'getSignedUrl', 'getStats', 'getConfig', 'updateConfig'
        ];
        
        const missingMethods = [];
        for (const method of requiredMethods) {
            if (!response.body.includes(method)) {
                missingMethods.push(method);
            }
        }
        
        if (missingMethods.length > 0) {
            throw new Error(`方法缺失: ${missingMethods.join(', ')}`);
        }
        
        console.log(colorize('✅ 存储方法验证通过', 'green'));
        console.log(colorize(`   📋 总方法数: ${requiredMethods.length} 个`, 'blue'));
        console.log(colorize(`   ✓ 覆盖率: 100%`, 'blue'));
        
        return true;
        
    } catch (error) {
        console.log(colorize(`❌ 存储方法验证失败: ${error.message}`, 'red'));
        return false;
    }
}

// 主测试函数
async function runTests() {
    console.log(colorize('🚀 开始MinIO功能测试', 'bright'));
    console.log(colorize(`📍 测试服务器: http://${TEST_CONFIG.host}:${TEST_CONFIG.port}`, 'blue'));
    console.log(colorize(`🗄️ MinIO服务器: ${MINIO_CONFIG.endpoint}`, 'blue'));
    console.log(colorize(`📦 存储桶: ${MINIO_CONFIG.bucketName}`, 'blue'));
    
    const results = [];
    
    // 检查MinIO服务器
    const minioServerRunning = await checkMinioServer();
    console.log(colorize(`🗄️ MinIO服务器状态: ${minioServerRunning ? '✅ 运行中' : '❌ 不可访问'}`, minioServerRunning ? 'green' : 'yellow'));
    
    if (!minioServerRunning) {
        console.log(colorize('⚠️ MinIO服务器不可访问，但继续测试页面功能', 'yellow'));
    }
    
    // 运行测试
    const tests = [
        { name: '页面配置验证', func: testPageConfiguration },
        { name: '页面组件验证', func: testPageComponents },
        { name: '存储方法覆盖', func: testStorageMethods }
    ];
    
    let passedCount = 0;
    let failedCount = 0;
    
    for (const test of tests) {
        const result = await test.func();
        results.push({ name: test.name, success: result });
        
        if (result) {
            passedCount++;
        } else {
            failedCount++;
        }
    }
    
    // 输出总结
    console.log(colorize('\n📊 测试结果总结', 'bright'));
    console.log(colorize(`✅ 通过: ${passedCount}`, 'green'));
    console.log(colorize(`❌ 失败: ${failedCount}`, 'red'));
    console.log(colorize(`📈 成功率: ${((passedCount / tests.length) * 100).toFixed(1)}%`, 'blue'));
    
    // 功能状态总结
    console.log(colorize('\n🎯 功能状态总结', 'bright'));
    console.log(colorize(`📄 测试页面: ${passedCount >= 1 ? '✅ 正常' : '❌ 异常'}`, passedCount >= 1 ? 'green' : 'red'));
    console.log(colorize(`⚙️ MinIO配置: ${results.find(r => r.name === '页面配置验证')?.success ? '✅ 已配置' : '❌ 未配置'}`, results.find(r => r.name === '页面配置验证')?.success ? 'green' : 'red'));
    console.log(colorize(`🧩 页面组件: ${results.find(r => r.name === '页面组件验证')?.success ? '✅ 完整' : '❌ 缺失'}`, results.find(r => r.name === '页面组件验证')?.success ? 'green' : 'red'));
    console.log(colorize(`📋 存储方法: ${results.find(r => r.name === '存储方法覆盖')?.success ? '✅ 完整' : '❌ 缺失'}`, results.find(r => r.name === '存储方法覆盖')?.success ? 'green' : 'red'));
    console.log(colorize(`🗄️ MinIO服务: ${minioServerRunning ? '✅ 可用' : '⚠️ 不可访问'}`, minioServerRunning ? 'green' : 'yellow'));
    
    if (passedCount === tests.length) {
        console.log(colorize('\n🎉 所有测试通过！MinIO配置和页面功能正常！', 'green'));
        
        if (minioServerRunning) {
            console.log(colorize('\n💡 建议操作:', 'cyan'));
            console.log(colorize('   1. 在浏览器中访问: http://localhost:3000', 'blue'));
            console.log(colorize('   2. 选择MinIO存储提供者', 'blue'));
            console.log(colorize('   3. 配置已预填，直接点击"保存并测试"', 'blue'));
            console.log(colorize('   4. 运行各项存储方法测试', 'blue'));
        } else {
            console.log(colorize('\n💡 注意事项:', 'yellow'));
            console.log(colorize('   - MinIO服务器不可访问，请确保MinIO服务正在运行', 'yellow'));
            console.log(colorize('   - 页面功能正常，可以进行模拟测试', 'yellow'));
        }
        
        process.exit(0);
    } else {
        console.log(colorize('\n⚠️ 部分测试失败，请检查页面配置', 'yellow'));
        process.exit(1);
    }
}

// 检查测试服务器
async function checkTestServer() {
    try {
        await makeRequest('/');
        return true;
    } catch (error) {
        return false;
    }
}

// 主函数
async function main() {
    console.log(colorize('🔍 检查测试服务器状态...', 'cyan'));
    
    const serverRunning = await checkTestServer();
    if (!serverRunning) {
        console.log(colorize('❌ 测试服务器未运行，请先启动服务器:', 'red'));
        console.log(colorize('   cd test && node server.js', 'yellow'));
        process.exit(1);
    }
    
    console.log(colorize('✅ 测试服务器运行正常', 'green'));
    
    await runTests();
}

// 运行测试
if (require.main === module) {
    main().catch(error => {
        console.error(colorize(`💥 测试运行失败: ${error.message}`, 'red'));
        process.exit(1);
    });
}

module.exports = { runTests, checkMinioServer };
