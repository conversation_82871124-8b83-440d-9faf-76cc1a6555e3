#!/usr/bin/env node

/**
 * 测试页面功能验证脚本
 * 验证存储提供者测试页面的基本功能
 */

const http = require('http');

// 测试配置
const TEST_CONFIG = {
    host: 'localhost',
    port: 3000,
    timeout: 10000
};

// 测试用例
const testCases = [
    {
        name: '主页面访问测试',
        path: '/',
        expectedStatus: 200,
        expectedContent: ['存储提供者完整方法测试', 'React', 'ComprehensiveStorageTest']
    },
    {
        name: '完整测试页面访问',
        path: '/comprehensive-storage-test.html',
        expectedStatus: 200,
        expectedContent: ['存储提供者完整方法测试', 'StorageProvider', 'TestRunner', 'IStorageProvider']
    },
    {
        name: '页面包含所有存储类型',
        path: '/comprehensive-storage-test.html',
        expectedStatus: 200,
        expectedContent: ['memoryStorage', 'localStorage', 'indexedDB', 'huaweiObs', 'minio']
    },
    {
        name: '页面包含所有测试方法',
        path: '/comprehensive-storage-test.html',
        expectedStatus: 200,
        expectedContent: [
            'initialize', 'dispose', 'testConnection',
            'get', 'put', 'delete', 'list',
            'getBatch', 'putBatch', 'deleteBatch',
            'getMetadata', 'getStream', 'putStream',
            'initiateMultipartUpload', 'uploadPart', 'completeMultipartUpload',
            'getSignedUrl', 'getStats', 'getConfig', 'updateConfig'
        ]
    },
    {
        name: '页面包含配置界面',
        path: '/comprehensive-storage-test.html',
        expectedStatus: 200,
        expectedContent: ['config-panel', 'huaweiObs', 'accessKeyId', 'secretAccessKey', 'bucketName']
    }
];

// 颜色输出
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

function colorize(text, color) {
    return `${colors[color]}${text}${colors.reset}`;
}

// HTTP请求函数
function makeRequest(path) {
    return new Promise((resolve, reject) => {
        const options = {
            hostname: TEST_CONFIG.host,
            port: TEST_CONFIG.port,
            path: path,
            method: 'GET',
            timeout: TEST_CONFIG.timeout
        };

        const req = http.request(options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                resolve({
                    statusCode: res.statusCode,
                    headers: res.headers,
                    body: data
                });
            });
        });

        req.on('error', (err) => {
            reject(err);
        });

        req.on('timeout', () => {
            req.destroy();
            reject(new Error('Request timeout'));
        });

        req.end();
    });
}

// 运行单个测试
async function runTest(testCase) {
    console.log(`\n${colorize('🧪 运行测试:', 'cyan')} ${testCase.name}`);
    
    try {
        const response = await makeRequest(testCase.path);
        
        // 检查状态码
        if (response.statusCode !== testCase.expectedStatus) {
            throw new Error(`状态码不匹配: 期望 ${testCase.expectedStatus}, 实际 ${response.statusCode}`);
        }
        
        // 检查内容
        const missingContent = [];
        for (const content of testCase.expectedContent) {
            if (!response.body.includes(content)) {
                missingContent.push(content);
            }
        }
        
        if (missingContent.length > 0) {
            throw new Error(`缺少预期内容: ${missingContent.join(', ')}`);
        }
        
        console.log(`   ${colorize('✅ 测试通过', 'green')}`);
        console.log(`   ${colorize('📊 状态码:', 'blue')} ${response.statusCode}`);
        console.log(`   ${colorize('📄 内容长度:', 'blue')} ${response.body.length} 字符`);
        console.log(`   ${colorize('✓ 包含内容:', 'blue')} ${testCase.expectedContent.length} 项检查通过`);
        
        return { success: true, testCase };
        
    } catch (error) {
        console.log(`   ${colorize('❌ 测试失败', 'red')}: ${error.message}`);
        return { success: false, testCase, error };
    }
}

// 运行所有测试
async function runAllTests() {
    console.log(colorize('🚀 开始运行存储提供者测试页面功能验证', 'bright'));
    console.log(colorize(`📍 测试服务器: http://${TEST_CONFIG.host}:${TEST_CONFIG.port}`, 'blue'));
    console.log(colorize(`📋 测试用例数量: ${testCases.length}`, 'blue'));
    
    const results = [];
    let passedCount = 0;
    let failedCount = 0;
    
    for (const testCase of testCases) {
        const result = await runTest(testCase);
        results.push(result);
        
        if (result.success) {
            passedCount++;
        } else {
            failedCount++;
        }
    }
    
    // 输出总结
    console.log(`\n${colorize('📊 测试结果总结', 'bright')}`);
    console.log(`${colorize('✅ 通过:', 'green')} ${passedCount}`);
    console.log(`${colorize('❌ 失败:', 'red')} ${failedCount}`);
    console.log(`${colorize('📈 成功率:', 'blue')} ${((passedCount / testCases.length) * 100).toFixed(1)}%`);
    
    if (failedCount > 0) {
        console.log(`\n${colorize('❌ 失败的测试:', 'red')}`);
        results.filter(r => !r.success).forEach(result => {
            console.log(`   - ${result.testCase.name}: ${result.error.message}`);
        });
    }
    
    // 功能验证总结
    console.log(`\n${colorize('🎯 功能验证总结', 'bright')}`);
    console.log(`${colorize('📄 页面访问:', 'blue')} ${passedCount >= 2 ? '✅ 正常' : '❌ 异常'}`);
    console.log(`${colorize('🧪 测试方法:', 'blue')} ${results.find(r => r.testCase.name.includes('测试方法'))?.success ? '✅ 完整' : '❌ 缺失'}`);
    console.log(`${colorize('⚙️ 配置界面:', 'blue')} ${results.find(r => r.testCase.name.includes('配置界面'))?.success ? '✅ 可用' : '❌ 不可用'}`);
    console.log(`${colorize('💾 存储类型:', 'blue')} ${results.find(r => r.testCase.name.includes('存储类型'))?.success ? '✅ 完整' : '❌ 缺失'}`);
    
    if (passedCount === testCases.length) {
        console.log(`\n${colorize('🎉 所有测试通过！存储提供者测试页面功能正常！', 'green')}`);
        process.exit(0);
    } else {
        console.log(`\n${colorize('⚠️ 部分测试失败，请检查页面功能', 'yellow')}`);
        process.exit(1);
    }
}

// 检查服务器是否运行
async function checkServer() {
    try {
        await makeRequest('/');
        return true;
    } catch (error) {
        return false;
    }
}

// 主函数
async function main() {
    console.log(colorize('🔍 检查测试服务器状态...', 'cyan'));
    
    const serverRunning = await checkServer();
    if (!serverRunning) {
        console.log(colorize('❌ 测试服务器未运行，请先启动服务器:', 'red'));
        console.log(colorize('   cd test && node server.js', 'yellow'));
        process.exit(1);
    }
    
    console.log(colorize('✅ 测试服务器运行正常', 'green'));
    
    await runAllTests();
}

// 运行测试
if (require.main === module) {
    main().catch(error => {
        console.error(colorize(`💥 测试运行失败: ${error.message}`, 'red'));
        process.exit(1);
    });
}

module.exports = { runAllTests, runTest, makeRequest };
