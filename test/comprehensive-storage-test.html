<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>存储提供者完整方法测试</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary: #667eea;
            --primary-dark: #5a6fd8;
            --success: #48bb78;
            --error: #f56565;
            --warning: #ed8936;
            --info: #4299e1;
            --bg-primary: #f7fafc;
            --bg-secondary: #edf2f7;
            --text-primary: #2d3748;
            --text-secondary: #4a5568;
            --border: #e2e8f0;
            --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, var(--primary) 0%, #764ba2 100%);
            min-height: 100vh;
            color: var(--text-primary);
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 16px;
            padding: 40px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: var(--shadow-lg);
        }

        .header h1 {
            font-size: 2.5em;
            font-weight: 700;
            background: linear-gradient(135deg, var(--primary) 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }

        .header p {
            color: var(--text-secondary);
            font-size: 1.1em;
        }

        .main-content {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 16px;
            padding: 30px;
            box-shadow: var(--shadow-lg);
        }

        .provider-selector {
            margin-bottom: 30px;
            padding: 20px;
            background: var(--bg-primary);
            border-radius: 12px;
            border: 1px solid var(--border);
        }

        .provider-selector h3 {
            margin-bottom: 15px;
            color: var(--text-primary);
        }

        .provider-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .provider-card {
            padding: 15px;
            background: white;
            border-radius: 8px;
            border: 2px solid var(--border);
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .provider-card:hover {
            border-color: var(--primary);
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }

        .provider-card.active {
            border-color: var(--primary);
            background: rgba(102, 126, 234, 0.1);
        }

        .provider-icon {
            font-size: 2em;
            margin-bottom: 10px;
        }

        .provider-name {
            font-weight: 600;
            color: var(--text-primary);
        }

        .test-sections {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .test-section {
            background: var(--bg-primary);
            border-radius: 12px;
            padding: 25px;
            border: 1px solid var(--border);
        }

        .test-section h4 {
            margin-bottom: 20px;
            color: var(--text-primary);
            font-size: 1.2em;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .test-methods {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .method-test {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px;
            background: white;
            border-radius: 8px;
            border: 1px solid var(--border);
            transition: all 0.3s ease;
        }

        .method-test:hover {
            box-shadow: var(--shadow);
        }

        .method-info {
            flex: 1;
        }

        .method-name {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
        }

        .method-desc {
            font-size: 0.9em;
            color: var(--text-secondary);
        }

        .method-status {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        .status-idle {
            background: var(--info);
        }

        .status-testing {
            background: var(--warning);
            animation: spin 1s linear infinite;
        }

        .status-success {
            background: var(--success);
        }

        .status-error {
            background: var(--error);
            animation: shake 0.5s ease-in-out;
        }

        .test-btn {
            background: var(--primary);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9em;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .test-btn:hover {
            background: var(--primary-dark);
            transform: translateY(-1px);
        }

        .test-btn:disabled {
            background: #a0aec0;
            cursor: not-allowed;
            transform: none;
        }

        .control-panel {
            background: var(--bg-primary);
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 30px;
            border: 1px solid var(--border);
        }

        .config-panel {
            background: var(--bg-primary);
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 30px;
            border: 1px solid var(--border);
            display: none;
        }

        .config-panel.show {
            display: block;
        }

        .config-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .form-group label {
            font-weight: 600;
            color: var(--text-primary);
            font-size: 0.9em;
        }

        .form-group input,
        .form-group select {
            padding: 12px;
            border: 1px solid var(--border);
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-group input[type="password"] {
            font-family: monospace;
        }

        .config-actions {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
        }

        .config-btn {
            background: var(--primary);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .config-btn:hover {
            background: var(--primary-dark);
            transform: translateY(-1px);
        }

        .config-btn.secondary {
            background: var(--text-secondary);
        }

        .config-btn.secondary:hover {
            background: var(--text-primary);
        }

        .config-status {
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: none;
        }

        .config-status.success {
            background: rgba(72, 187, 120, 0.1);
            border: 1px solid var(--success);
            color: var(--success);
            display: block;
        }

        .config-status.error {
            background: rgba(245, 101, 101, 0.1);
            border: 1px solid var(--error);
            color: var(--error);
            display: block;
        }

        .control-panel h3 {
            margin-bottom: 20px;
            color: var(--text-primary);
        }

        .control-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }

        .control-btn {
            background: var(--primary);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .control-btn:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }

        .control-btn.success {
            background: var(--success);
        }

        .control-btn.warning {
            background: var(--warning);
        }

        .control-btn.danger {
            background: var(--error);
        }

        .stats-panel {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            border: 1px solid var(--border);
            box-shadow: var(--shadow);
        }

        .stat-number {
            font-size: 2em;
            font-weight: 700;
            color: var(--primary);
            margin-bottom: 5px;
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .log-terminal {
            background: #1a202c;
            border-radius: 12px;
            padding: 20px;
            margin-top: 30px;
            border: 1px solid #2d3748;
        }

        .terminal-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid #2d3748;
        }

        .terminal-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }

        .terminal-dot.red { background: #ff5f56; }
        .terminal-dot.yellow { background: #ffbd2e; }
        .terminal-dot.green { background: #27ca3f; }

        .terminal-title {
            color: #a0aec0;
            font-size: 0.9em;
            margin-left: 10px;
        }

        .terminal-content {
            max-height: 400px;
            overflow-y: auto;
            font-family: 'SF Mono', 'Monaco', monospace;
            font-size: 13px;
            line-height: 1.6;
        }

        .log-entry {
            margin: 3px 0;
            padding: 5px 10px;
            border-radius: 4px;
            animation: logAppear 0.3s ease-out;
        }

        @keyframes logAppear {
            from { opacity: 0; transform: translateX(-20px); }
            to { opacity: 1; transform: translateX(0); }
        }

        .log-success { color: #68d391; border-left: 3px solid var(--success); padding-left: 15px; }
        .log-error { color: #fc8181; border-left: 3px solid var(--error); padding-left: 15px; }
        .log-warning { color: #f6e05e; border-left: 3px solid var(--warning); padding-left: 15px; }
        .log-info { color: #63b3ed; border-left: 3px solid var(--info); padding-left: 15px; }

        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.2); opacity: 0.7; }
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-3px); }
            75% { transform: translateX(3px); }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container { padding: 15px; }
            .header { padding: 30px 20px; }
            .header h1 { font-size: 2em; }
            .test-sections { grid-template-columns: 1fr; }
            .provider-grid { grid-template-columns: repeat(2, 1fr); }
            .stats-panel { grid-template-columns: repeat(2, 1fr); }
        }

        @media (max-width: 480px) {
            .header h1 { font-size: 1.8em; }
            .provider-grid { grid-template-columns: 1fr; }
            .stats-panel { grid-template-columns: 1fr; }
            .control-buttons { flex-direction: column; }
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect, useRef } = React;

        // 存储提供者类型定义
        const StorageType = {
            MEMORY_STORAGE: 'memoryStorage',
            LOCAL_STORAGE: 'localStorage',
            INDEXED_DB: 'indexedDB',
            HUAWEI_OBS: 'huaweiObs',
            MINIO: 'minio'
        };

        // 真实/模拟存储提供者实现
        class StorageProvider {
            constructor(name, type, icon) {
                this.name = name;
                this.type = type;
                this.icon = icon;
                this.isInitialized = false;
                this.data = new Map();
                this.metadata = new Map();
                this.multipartUploads = new Map();
                this.isRealService = (type === 'huaweiObs' || type === 'minio');
            }

            // 生命周期管理
            async initialize(config) {
                await this.delay(200);
                this.config = config;

                if (this.isRealService) {
                    try {
                        // 对于真实服务，进行连接测试
                        await this.testRealServiceConnection();
                        this.isInitialized = true;
                        return { success: true };
                    } catch (error) {
                        return { success: false, error: error.message };
                    }
                } else {
                    // 模拟服务直接初始化成功
                    this.isInitialized = true;
                    return { success: true };
                }
            }

            async testRealServiceConnection() {
                if (this.type === 'huaweiObs') {
                    return this.testHuaweiObsConnection();
                } else if (this.type === 'minio') {
                    return this.testMinioConnection();
                }
            }

            async testHuaweiObsConnection() {
                // 使用华为云OBS SDK进行连接测试
                // 注意：根据运行环境选择对应的SDK
                // 浏览器环境: npm install esdk-obs-browserjs
                // Node.js环境: npm install esdk-obs-nodejs
                const { accessKeyId, secretAccessKey, endpoint, bucketName, region } = this.config;

                if (!accessKeyId || !secretAccessKey || !bucketName) {
                    throw new Error('华为云OBS配置不完整');
                }

                // 模拟连接测试（实际项目中应该使用真实的SDK）
                try {
                    // 浏览器环境的真实代码示例：
                    // import ObsClient from 'esdk-obs-browserjs';
                    // const obsClient = new ObsClient({
                    //     access_key_id: accessKeyId,
                    //     secret_access_key: secretAccessKey,
                    //     server: endpoint
                    // });
                    // await obsClient.headBucket({ Bucket: bucketName });

                    // Node.js环境的真实代码示例：
                    // const ObsClient = require('esdk-obs-nodejs');
                    // const obsClient = new ObsClient({
                    //     access_key_id: accessKeyId,
                    //     secret_access_key: secretAccessKey,
                    //     server: endpoint
                    // });
                    // await obsClient.headBucket({ Bucket: bucketName });

                    console.log('华为云OBS连接测试 - 模拟成功');
                    return true;
                } catch (error) {
                    throw new Error(`华为云OBS连接失败: ${error.message}`);
                }
            }

            async testMinioConnection() {
                // 使用MinIO SDK进行连接测试
                const { accessKeyId, secretAccessKey, endpoint, bucketName, useSSL } = this.config;

                if (!accessKeyId || !secretAccessKey || !bucketName) {
                    throw new Error('MinIO配置不完整');
                }

                try {
                    // 这里应该是真实的MinIO连接测试代码
                    // const minioClient = new Minio.Client({
                    //     endPoint: endpoint.replace(/^https?:\/\//, ''),
                    //     port: useSSL ? 443 : 9000,
                    //     useSSL: useSSL,
                    //     accessKey: accessKeyId,
                    //     secretKey: secretAccessKey
                    // });
                    // await minioClient.bucketExists(bucketName);

                    console.log('MinIO连接测试 - 模拟成功');
                    return true;
                } catch (error) {
                    throw new Error(`MinIO连接失败: ${error.message}`);
                }
            }

            async dispose() {
                await this.delay(100);
                this.isInitialized = false;
                this.data.clear();
                this.metadata.clear();
                return { success: true };
            }

            // 连接测试
            async testConnection() {
                await this.delay(150);
                return this.isInitialized;
            }

            // 基础CRUD操作
            async get(key, options = {}) {
                await this.delay(100);
                if (!this.data.has(key)) {
                    return { success: false, error: new Error('对象不存在') };
                }

                let data = this.data.get(key);

                // 处理范围请求
                if (options.range && typeof data === 'string') {
                    const { start, end } = options.range;
                    data = data.substring(start, end + 1);
                }

                return {
                    success: true,
                    data,
                    metadata: this.metadata.get(key) || {}
                };
            }

            async put(key, data, options = {}) {
                await this.delay(120);
                this.data.set(key, data);

                const metadata = {
                    size: JSON.stringify(data).length,
                    lastModified: new Date(),
                    etag: `"${Math.random().toString(36).substr(2, 9)}"`,
                    contentType: options.contentType || 'application/json'
                };

                this.metadata.set(key, metadata);

                return {
                    success: true,
                    metadata: { etag: metadata.etag }
                };
            }

            async delete(key) {
                await this.delay(80);
                const existed = this.data.has(key);
                this.data.delete(key);
                this.metadata.delete(key);

                return {
                    success: true,
                    existed
                };
            }

            async list(prefix = '', options = {}) {
                await this.delay(100);
                let keys = Array.from(this.data.keys());

                if (prefix) {
                    keys = keys.filter(key => key.startsWith(prefix));
                }

                const maxKeys = options.maxKeys || 1000;
                keys = keys.slice(0, maxKeys);

                return {
                    success: true,
                    data: keys,
                    truncated: keys.length === maxKeys
                };
            }

            // 批量操作
            async getBatch(keys, options = {}) {
                await this.delay(200);
                const results = {};
                const errors = [];

                for (const key of keys) {
                    try {
                        const result = await this.get(key);
                        if (result.success) {
                            results[key] = result.data;
                        } else {
                            errors.push(`${key}: ${result.error.message}`);
                            if (!options.continueOnError) break;
                        }
                    } catch (error) {
                        errors.push(`${key}: ${error.message}`);
                        if (!options.continueOnError) break;
                    }
                }

                return {
                    success: errors.length === 0 || options.continueOnError,
                    data: results,
                    errors
                };
            }

            async putBatch(items, options = {}) {
                await this.delay(250);
                const errors = [];

                for (const [key, data] of Object.entries(items)) {
                    try {
                        const result = await this.put(key, data);
                        if (!result.success) {
                            errors.push(`${key}: 写入失败`);
                            if (!options.continueOnError) break;
                        }
                    } catch (error) {
                        errors.push(`${key}: ${error.message}`);
                        if (!options.continueOnError) break;
                    }
                }

                return {
                    success: errors.length === 0 || options.continueOnError,
                    errors
                };
            }

            async deleteBatch(keys, options = {}) {
                await this.delay(200);
                const errors = [];

                for (const key of keys) {
                    try {
                        const result = await this.delete(key);
                        if (!result.success) {
                            errors.push(`${key}: 删除失败`);
                            if (!options.continueOnError) break;
                        }
                    } catch (error) {
                        errors.push(`${key}: ${error.message}`);
                        if (!options.continueOnError) break;
                    }
                }

                return {
                    success: errors.length === 0 || options.continueOnError,
                    errors
                };
            }

            // 元数据操作
            async getMetadata(key) {
                await this.delay(80);
                if (!this.data.has(key)) {
                    return { success: false, error: new Error('对象不存在') };
                }

                return {
                    success: true,
                    data: this.metadata.get(key) || {}
                };
            }

            // 流式操作（模拟）
            async getStream(key, options = {}) {
                await this.delay(150);
                if (this.type === 'memoryStorage' || this.type === 'localStorage') {
                    throw new Error('此存储类型不支持流操作');
                }

                // 模拟返回ReadableStream
                return new ReadableStream({
                    start(controller) {
                        const data = `Mock stream data for ${key}`;
                        controller.enqueue(new TextEncoder().encode(data));
                        controller.close();
                    }
                });
            }

            async putStream(key, stream, options = {}) {
                await this.delay(200);
                if (this.type === 'memoryStorage' || this.type === 'localStorage') {
                    throw new Error('此存储类型不支持流操作');
                }

                // 模拟流处理
                return { success: true };
            }

            // 分块上传操作（模拟）
            async initiateMultipartUpload(key, options = {}) {
                await this.delay(100);
                if (this.type === 'memoryStorage' || this.type === 'localStorage') {
                    throw new Error('此存储类型不支持分块上传');
                }

                const uploadId = `upload_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
                this.multipartUploads.set(uploadId, {
                    key,
                    parts: [],
                    initiated: new Date()
                });

                return { success: true, data: uploadId };
            }

            async uploadPart(key, uploadId, partNumber, data) {
                await this.delay(150);
                if (!this.multipartUploads.has(uploadId)) {
                    return { success: false, error: new Error('上传ID不存在') };
                }

                const upload = this.multipartUploads.get(uploadId);
                const partInfo = {
                    partNumber,
                    etag: `"part_${partNumber}_${Math.random().toString(36).substr(2, 9)}"`,
                    size: data.length
                };

                upload.parts.push(partInfo);

                return { success: true, data: partInfo };
            }

            async completeMultipartUpload(key, uploadId, parts) {
                await this.delay(200);
                if (!this.multipartUploads.has(uploadId)) {
                    return { success: false, error: new Error('上传ID不存在') };
                }

                // 模拟合并分块
                const combinedData = `Combined data from ${parts.length} parts`;
                await this.put(key, combinedData);

                this.multipartUploads.delete(uploadId);

                return { success: true };
            }

            async abortMultipartUpload(key, uploadId) {
                await this.delay(100);
                const existed = this.multipartUploads.has(uploadId);
                this.multipartUploads.delete(uploadId);

                return { success: true, existed };
            }

            async listMultipartUploads(prefix = '') {
                await this.delay(120);
                const uploads = Array.from(this.multipartUploads.entries())
                    .filter(([_, upload]) => !prefix || upload.key.startsWith(prefix))
                    .map(([uploadId, upload]) => ({
                        uploadId,
                        key: upload.key,
                        initiated: upload.initiated
                    }));

                return { success: true, data: uploads };
            }

            // URL生成
            async getSignedUrl(key, options = {}) {
                await this.delay(50);
                const expires = options.expires || 3600;
                const operation = options.operation || 'GET';

                return `https://mock-${this.type}.example.com/${key}?expires=${expires}&op=${operation}&sig=mock_signature`;
            }

            // 统计信息
            async getStats() {
                await this.delay(100);
                let totalSize = 0;
                for (const data of this.data.values()) {
                    totalSize += JSON.stringify(data).length;
                }

                return {
                    success: true,
                    data: {
                        totalObjects: this.data.size,
                        totalSize,
                        usedSpace: totalSize,
                        availableSpace: 1024 * 1024 * 100, // 100MB
                        lastModified: new Date()
                    }
                };
            }

            // 配置管理
            getConfig() {
                return { ...this.config };
            }

            async updateConfig(config) {
                await this.delay(100);
                this.config = { ...this.config, ...config };
                return { success: true };
            }

            // 辅助方法
            delay(ms) {
                return new Promise(resolve => setTimeout(resolve, ms));
            }
        }

        // 存储工厂
        const StorageFactory = {
            providers: {
                [StorageType.MEMORY_STORAGE]: () => new StorageProvider('内存存储提供者', StorageType.MEMORY_STORAGE, '💾'),
                [StorageType.LOCAL_STORAGE]: () => new StorageProvider('本地存储提供者', StorageType.LOCAL_STORAGE, '🏠'),
                [StorageType.INDEXED_DB]: () => new StorageProvider('浏览器数据库提供者', StorageType.INDEXED_DB, '🗄️'),
                [StorageType.HUAWEI_OBS]: () => new StorageProvider('华为云OBS提供者', StorageType.HUAWEI_OBS, '☁️'),
                [StorageType.MINIO]: () => new StorageProvider('MinIO提供者', StorageType.MINIO, '📦')
            },

            create(type) {
                const factory = this.providers[type];
                if (!factory) {
                    throw new Error(`不支持的存储类型: ${type}`);
                }
                return factory();
            },

            getSupportedTypes() {
                return Object.keys(this.providers);
            }
        };

        // 测试方法定义
        const testMethods = {
            lifecycle: [
                { name: 'initialize', desc: '初始化存储提供者', category: 'lifecycle' },
                { name: 'dispose', desc: '释放资源', category: 'lifecycle' },
                { name: 'testConnection', desc: '测试连接', category: 'lifecycle' }
            ],
            basic: [
                { name: 'get', desc: '获取对象', category: 'basic' },
                { name: 'put', desc: '存储对象', category: 'basic' },
                { name: 'delete', desc: '删除对象', category: 'basic' },
                { name: 'list', desc: '列出对象', category: 'basic' }
            ],
            batch: [
                { name: 'getBatch', desc: '批量获取', category: 'batch' },
                { name: 'putBatch', desc: '批量存储', category: 'batch' },
                { name: 'deleteBatch', desc: '批量删除', category: 'batch' }
            ],
            metadata: [
                { name: 'getMetadata', desc: '获取元数据', category: 'metadata' }
            ],
            stream: [
                { name: 'getStream', desc: '获取流', category: 'stream' },
                { name: 'putStream', desc: '存储流', category: 'stream' }
            ],
            multipart: [
                { name: 'initiateMultipartUpload', desc: '初始化分块上传', category: 'multipart' },
                { name: 'uploadPart', desc: '上传分块', category: 'multipart' },
                { name: 'completeMultipartUpload', desc: '完成分块上传', category: 'multipart' },
                { name: 'abortMultipartUpload', desc: '中止分块上传', category: 'multipart' },
                { name: 'listMultipartUploads', desc: '列出分块上传', category: 'multipart' }
            ],
            advanced: [
                { name: 'getSignedUrl', desc: '生成签名URL', category: 'advanced' },
                { name: 'getStats', desc: '获取统计信息', category: 'advanced' },
                { name: 'getConfig', desc: '获取配置', category: 'advanced' },
                { name: 'updateConfig', desc: '更新配置', category: 'advanced' }
            ]
        };

        // 获取所有测试方法
        const getAllMethods = () => {
            return Object.values(testMethods).flat();
        };

        // 测试执行器
        class TestRunner {
            constructor(provider, onLog) {
                this.provider = provider;
                this.onLog = onLog;
                this.testData = {
                    'test-key-1': { id: 1, name: 'Test Object 1', data: 'Hello World' },
                    'test-key-2': { id: 2, name: 'Test Object 2', data: 'Hello Universe' },
                    'test-key-3': { id: 3, name: 'Test Object 3', data: 'Hello Galaxy' }
                };
            }

            log(message, type = 'info') {
                this.onLog(message, type, this.provider.name);
            }

            async runTest(methodName) {
                this.log(`🔄 开始测试 ${methodName}...`, 'info');

                try {
                    const result = await this[`test_${methodName}`]();
                    if (result.success) {
                        this.log(`✅ ${methodName} 测试通过`, 'success');
                        return { success: true };
                    } else {
                        this.log(`❌ ${methodName} 测试失败: ${result.error}`, 'error');
                        return { success: false, error: result.error };
                    }
                } catch (error) {
                    this.log(`❌ ${methodName} 测试异常: ${error.message}`, 'error');
                    return { success: false, error: error.message };
                }
            }

            // 生命周期测试
            async test_initialize() {
                const config = {
                    type: this.provider.type,
                    name: `test-${this.provider.type}`,
                    timeout: 10000
                };

                const result = await this.provider.initialize(config);
                return result.success ?
                    { success: true } :
                    { success: false, error: '初始化失败' };
            }

            async test_dispose() {
                const result = await this.provider.dispose();
                return result.success ?
                    { success: true } :
                    { success: false, error: '释放资源失败' };
            }

            async test_testConnection() {
                const connected = await this.provider.testConnection();
                return connected ?
                    { success: true } :
                    { success: false, error: '连接测试失败' };
            }

            // 基础操作测试
            async test_get() {
                // 先存储一个测试对象
                await this.provider.put('test-get-key', this.testData['test-key-1']);

                const result = await this.provider.get('test-get-key');
                if (!result.success) {
                    return { success: false, error: '获取对象失败' };
                }

                const data = result.data;
                if (JSON.stringify(data) !== JSON.stringify(this.testData['test-key-1'])) {
                    return { success: false, error: '获取的数据不匹配' };
                }

                return { success: true };
            }

            async test_put() {
                const result = await this.provider.put('test-put-key', this.testData['test-key-1']);
                return result.success ?
                    { success: true } :
                    { success: false, error: '存储对象失败' };
            }

            async test_delete() {
                // 先存储一个测试对象
                await this.provider.put('test-delete-key', this.testData['test-key-1']);

                const result = await this.provider.delete('test-delete-key');
                return result.success ?
                    { success: true } :
                    { success: false, error: '删除对象失败' };
            }

            async test_list() {
                // 先存储几个测试对象
                for (const [key, data] of Object.entries(this.testData)) {
                    await this.provider.put(`list-test-${key}`, data);
                }

                const result = await this.provider.list('list-test-');
                if (!result.success) {
                    return { success: false, error: '列出对象失败' };
                }

                if (result.data.length < 3) {
                    return { success: false, error: '列出的对象数量不正确' };
                }

                return { success: true };
            }

            // 批量操作测试
            async test_getBatch() {
                // 先存储测试数据
                for (const [key, data] of Object.entries(this.testData)) {
                    await this.provider.put(`batch-get-${key}`, data);
                }

                const keys = Object.keys(this.testData).map(key => `batch-get-${key}`);
                const result = await this.provider.getBatch(keys);

                if (!result.success) {
                    return { success: false, error: '批量获取失败' };
                }

                if (Object.keys(result.data).length !== keys.length) {
                    return { success: false, error: '批量获取的对象数量不正确' };
                }

                return { success: true };
            }

            async test_putBatch() {
                const batchData = {};
                for (const [key, data] of Object.entries(this.testData)) {
                    batchData[`batch-put-${key}`] = data;
                }

                const result = await this.provider.putBatch(batchData);
                return result.success ?
                    { success: true } :
                    { success: false, error: '批量存储失败' };
            }

            async test_deleteBatch() {
                // 先存储测试数据
                const keys = [];
                for (const [key, data] of Object.entries(this.testData)) {
                    const deleteKey = `batch-delete-${key}`;
                    await this.provider.put(deleteKey, data);
                    keys.push(deleteKey);
                }

                const result = await this.provider.deleteBatch(keys);
                return result.success ?
                    { success: true } :
                    { success: false, error: '批量删除失败' };
            }

            // 元数据测试
            async test_getMetadata() {
                // 先存储一个测试对象
                await this.provider.put('metadata-test-key', this.testData['test-key-1']);

                const result = await this.provider.getMetadata('metadata-test-key');
                if (!result.success) {
                    return { success: false, error: '获取元数据失败' };
                }

                const metadata = result.data;
                if (!metadata.size || !metadata.lastModified || !metadata.etag) {
                    return { success: false, error: '元数据不完整' };
                }

                return { success: true };
            }

            // 流操作测试
            async test_getStream() {
                try {
                    await this.provider.put('stream-test-key', 'Stream test data');
                    const stream = await this.provider.getStream('stream-test-key');

                    if (!(stream instanceof ReadableStream)) {
                        return { success: false, error: '返回的不是ReadableStream' };
                    }

                    return { success: true };
                } catch (error) {
                    if (error.message.includes('不支持流操作')) {
                        this.log(`ℹ️ ${this.provider.name} 不支持流操作，跳过测试`, 'info');
                        return { success: true };
                    }
                    throw error;
                }
            }

            async test_putStream() {
                try {
                    const stream = new ReadableStream({
                        start(controller) {
                            controller.enqueue(new TextEncoder().encode('Stream test data'));
                            controller.close();
                        }
                    });

                    const result = await this.provider.putStream('stream-put-test-key', stream);
                    return result.success ?
                        { success: true } :
                        { success: false, error: '流存储失败' };
                } catch (error) {
                    if (error.message.includes('不支持流操作')) {
                        this.log(`ℹ️ ${this.provider.name} 不支持流操作，跳过测试`, 'info');
                        return { success: true };
                    }
                    throw error;
                }
            }

            // 分块上传测试
            async test_initiateMultipartUpload() {
                try {
                    const result = await this.provider.initiateMultipartUpload('multipart-test-key');
                    if (!result.success) {
                        return { success: false, error: '初始化分块上传失败' };
                    }

                    if (!result.data || typeof result.data !== 'string') {
                        return { success: false, error: '未返回有效的上传ID' };
                    }

                    return { success: true, uploadId: result.data };
                } catch (error) {
                    if (error.message.includes('不支持分块上传')) {
                        this.log(`ℹ️ ${this.provider.name} 不支持分块上传，跳过测试`, 'info');
                        return { success: true };
                    }
                    throw error;
                }
            }

            async test_uploadPart() {
                try {
                    const initResult = await this.provider.initiateMultipartUpload('multipart-part-test-key');
                    if (!initResult.success) {
                        return { success: false, error: '初始化分块上传失败' };
                    }

                    const uploadId = initResult.data;
                    const partData = new Uint8Array([1, 2, 3, 4, 5]);

                    const result = await this.provider.uploadPart('multipart-part-test-key', uploadId, 1, partData);
                    if (!result.success) {
                        return { success: false, error: '上传分块失败' };
                    }

                    if (!result.data || !result.data.etag) {
                        return { success: false, error: '未返回有效的分块信息' };
                    }

                    return { success: true };
                } catch (error) {
                    if (error.message.includes('不支持分块上传')) {
                        this.log(`ℹ️ ${this.provider.name} 不支持分块上传，跳过测试`, 'info');
                        return { success: true };
                    }
                    throw error;
                }
            }

            async test_completeMultipartUpload() {
                try {
                    const initResult = await this.provider.initiateMultipartUpload('multipart-complete-test-key');
                    if (!initResult.success) {
                        return { success: false, error: '初始化分块上传失败' };
                    }

                    const uploadId = initResult.data;
                    const partData = new Uint8Array([1, 2, 3, 4, 5]);

                    const partResult = await this.provider.uploadPart('multipart-complete-test-key', uploadId, 1, partData);
                    if (!partResult.success) {
                        return { success: false, error: '上传分块失败' };
                    }

                    const parts = [partResult.data];
                    const result = await this.provider.completeMultipartUpload('multipart-complete-test-key', uploadId, parts);

                    return result.success ?
                        { success: true } :
                        { success: false, error: '完成分块上传失败' };
                } catch (error) {
                    if (error.message.includes('不支持分块上传')) {
                        this.log(`ℹ️ ${this.provider.name} 不支持分块上传，跳过测试`, 'info');
                        return { success: true };
                    }
                    throw error;
                }
            }

            async test_abortMultipartUpload() {
                try {
                    const initResult = await this.provider.initiateMultipartUpload('multipart-abort-test-key');
                    if (!initResult.success) {
                        return { success: false, error: '初始化分块上传失败' };
                    }

                    const uploadId = initResult.data;
                    const result = await this.provider.abortMultipartUpload('multipart-abort-test-key', uploadId);

                    return result.success ?
                        { success: true } :
                        { success: false, error: '中止分块上传失败' };
                } catch (error) {
                    if (error.message.includes('不支持分块上传')) {
                        this.log(`ℹ️ ${this.provider.name} 不支持分块上传，跳过测试`, 'info');
                        return { success: true };
                    }
                    throw error;
                }
            }

            async test_listMultipartUploads() {
                try {
                    const result = await this.provider.listMultipartUploads();
                    if (!result.success) {
                        return { success: false, error: '列出分块上传失败' };
                    }

                    if (!Array.isArray(result.data)) {
                        return { success: false, error: '返回的不是数组' };
                    }

                    return { success: true };
                } catch (error) {
                    if (error.message.includes('不支持分块上传')) {
                        this.log(`ℹ️ ${this.provider.name} 不支持分块上传，跳过测试`, 'info');
                        return { success: true };
                    }
                    throw error;
                }
            }

            // 高级功能测试
            async test_getSignedUrl() {
                const url = await this.provider.getSignedUrl('signed-url-test-key', {
                    expires: 3600,
                    operation: 'GET'
                });

                if (!url || typeof url !== 'string' || !url.startsWith('http')) {
                    return { success: false, error: '生成的签名URL无效' };
                }

                return { success: true };
            }

            async test_getStats() {
                const result = await this.provider.getStats();
                if (!result.success) {
                    return { success: false, error: '获取统计信息失败' };
                }

                const stats = result.data;
                if (typeof stats.totalObjects !== 'number' ||
                    typeof stats.totalSize !== 'number' ||
                    typeof stats.usedSpace !== 'number' ||
                    typeof stats.availableSpace !== 'number') {
                    return { success: false, error: '统计信息格式不正确' };
                }

                return { success: true };
            }

            async test_getConfig() {
                const config = this.provider.getConfig();
                if (!config || typeof config !== 'object') {
                    return { success: false, error: '获取配置失败' };
                }

                return { success: true };
            }

            async test_updateConfig() {
                const newConfig = { timeout: 15000 };
                const result = await this.provider.updateConfig(newConfig);

                return result.success ?
                    { success: true } :
                    { success: false, error: '更新配置失败' };
            }
        }

        // React组件
        function ComprehensiveStorageTest() {
            const [selectedProvider, setSelectedProvider] = useState(null);
            const [provider, setProvider] = useState(null);
            const [testRunner, setTestRunner] = useState(null);
            const [logs, setLogs] = useState([]);
            const [methodStates, setMethodStates] = useState({});
            const [stats, setStats] = useState({
                total: 0,
                tested: 0,
                passed: 0,
                failed: 0
            });
            const [showConfig, setShowConfig] = useState(false);
            const [configStatus, setConfigStatus] = useState({ show: false, type: '', message: '' });
            const [configs, setConfigs] = useState({
                huaweiObs: {
                    accessKeyId: '',
                    secretAccessKey: '',
                    endpoint: 'https://obs.cn-north-4.myhuaweicloud.com',
                    bucketName: '',
                    region: 'cn-north-4'
                },
                minio: {
                    accessKeyId: 'FsYFOP9cOOYDyfM9odzX',
                    secretAccessKey: 'AcQaaZTcoOh6GKMuLKAcdIo4W0qcWQ8VWKkqQvLl',
                    endpoint: 'http://127.0.0.1:9000',
                    bucketName: 'eversnip',
                    region: 'us-east-1',
                    useSSL: false
                }
            });

            const terminalRef = useRef(null);

            const supportedTypes = StorageFactory.getSupportedTypes();
            const allMethods = getAllMethods();

            useEffect(() => {
                const totalMethods = allMethods.length;
                setStats(prev => ({ ...prev, total: totalMethods }));

                // 初始化方法状态
                const initialStates = {};
                allMethods.forEach(method => {
                    initialStates[method.name] = 'idle';
                });
                setMethodStates(initialStates);

                addLog('🚀 存储提供者完整方法测试系统已启动', 'info');
                addLog(`📋 检测到 ${supportedTypes.length} 个存储提供者`, 'info');
                addLog(`🧪 共 ${totalMethods} 个测试方法`, 'info');
                addLog('✨ 请选择一个存储提供者开始测试...', 'info');
            }, []);

            useEffect(() => {
                if (terminalRef.current) {
                    terminalRef.current.scrollTop = terminalRef.current.scrollHeight;
                }
            }, [logs]);

            const addLog = (message, type = 'info', provider = 'System') => {
                const timestamp = new Date().toLocaleTimeString();
                setLogs(prev => [...prev, {
                    id: Date.now() + Math.random(),
                    message: `[${provider}] ${message}`,
                    type,
                    timestamp
                }]);
            };

            const selectProvider = async (type) => {
                // 检查是否需要配置
                if ((type === StorageType.HUAWEI_OBS || type === StorageType.MINIO) && !isConfigured(type)) {
                    setSelectedProvider(type);
                    setShowConfig(true);
                    addLog(`⚙️ ${type} 需要配置认证信息，请填写配置`, 'warning');
                    return;
                }

                try {
                    addLog(`🔄 正在初始化 ${type} 提供者...`, 'info');

                    const newProvider = StorageFactory.create(type);
                    const newTestRunner = new TestRunner(newProvider, addLog);

                    // 获取配置
                    let config = {
                        type: type,
                        name: `test-${type}`,
                        timeout: 10000
                    };

                    // 为云存储添加认证配置
                    if (type === StorageType.HUAWEI_OBS) {
                        config = { ...config, ...configs.huaweiObs };
                    } else if (type === StorageType.MINIO) {
                        config = { ...config, ...configs.minio };
                    }

                    // 初始化提供者
                    await newTestRunner.runTest('initialize');

                    setSelectedProvider(type);
                    setProvider(newProvider);
                    setTestRunner(newTestRunner);
                    setShowConfig(false);

                    addLog(`✅ ${newProvider.name} 初始化完成，可以开始测试`, 'success');
                } catch (error) {
                    addLog(`❌ 初始化提供者失败: ${error.message}`, 'error');
                }
            };

            const isConfigured = (type) => {
                if (type === StorageType.HUAWEI_OBS) {
                    const config = configs.huaweiObs;
                    return config.accessKeyId && config.secretAccessKey && config.bucketName;
                } else if (type === StorageType.MINIO) {
                    const config = configs.minio;
                    return config.accessKeyId && config.secretAccessKey && config.bucketName;
                }
                return true;
            };

            const updateConfig = (type, field, value) => {
                setConfigs(prev => ({
                    ...prev,
                    [type]: {
                        ...prev[type],
                        [field]: value
                    }
                }));
            };

            const saveConfig = async () => {
                const type = selectedProvider;
                const config = configs[type === StorageType.HUAWEI_OBS ? 'huaweiObs' : 'minio'];

                // 验证必填字段
                if (!config.accessKeyId || !config.secretAccessKey || !config.bucketName) {
                    setConfigStatus({
                        show: true,
                        type: 'error',
                        message: '请填写所有必填字段'
                    });
                    return;
                }

                try {
                    setConfigStatus({
                        show: true,
                        type: 'success',
                        message: '配置已保存，正在初始化提供者...'
                    });

                    // 延迟一下再初始化
                    setTimeout(() => {
                        selectProvider(type);
                    }, 1000);
                } catch (error) {
                    setConfigStatus({
                        show: true,
                        type: 'error',
                        message: `保存配置失败: ${error.message}`
                    });
                }
            };

            const cancelConfig = () => {
                setShowConfig(false);
                setSelectedProvider(null);
                setConfigStatus({ show: false, type: '', message: '' });
            };

            const runSingleTest = async (methodName) => {
                if (!testRunner) {
                    addLog('❌ 请先选择一个存储提供者', 'error');
                    return;
                }

                setMethodStates(prev => ({ ...prev, [methodName]: 'testing' }));

                try {
                    const result = await testRunner.runTest(methodName);

                    if (result.success) {
                        setMethodStates(prev => ({ ...prev, [methodName]: 'success' }));
                        setStats(prev => ({
                            ...prev,
                            tested: prev.tested + 1,
                            passed: prev.passed + 1
                        }));
                    } else {
                        setMethodStates(prev => ({ ...prev, [methodName]: 'error' }));
                        setStats(prev => ({
                            ...prev,
                            tested: prev.tested + 1,
                            failed: prev.failed + 1
                        }));
                    }
                } catch (error) {
                    setMethodStates(prev => ({ ...prev, [methodName]: 'error' }));
                    setStats(prev => ({
                        ...prev,
                        tested: prev.tested + 1,
                        failed: prev.failed + 1
                    }));
                    addLog(`❌ 测试 ${methodName} 时发生异常: ${error.message}`, 'error');
                }
            };

            const runCategoryTests = async (category) => {
                if (!testRunner) {
                    addLog('❌ 请先选择一个存储提供者', 'error');
                    return;
                }

                const categoryMethods = testMethods[category] || [];
                addLog(`🎯 开始运行 ${category} 类别的所有测试...`, 'info');

                for (const method of categoryMethods) {
                    await runSingleTest(method.name);
                    await new Promise(resolve => setTimeout(resolve, 200)); // 短暂延迟
                }

                addLog(`🏁 ${category} 类别测试完成`, 'success');
            };

            const runAllTests = async () => {
                if (!testRunner) {
                    addLog('❌ 请先选择一个存储提供者', 'error');
                    return;
                }

                addLog('🚀 开始运行所有测试方法...', 'info');

                // 重置统计
                setStats(prev => ({ ...prev, tested: 0, passed: 0, failed: 0 }));

                // 按类别顺序执行测试
                const categories = ['lifecycle', 'basic', 'batch', 'metadata', 'stream', 'multipart', 'advanced'];

                for (const category of categories) {
                    await runCategoryTests(category);
                    await new Promise(resolve => setTimeout(resolve, 500)); // 类别间延迟
                }

                addLog('🎉 所有测试完成！', 'success');
            };

            const clearLogs = () => {
                setLogs([]);
                addLog('🧹 日志已清除', 'info');
            };

            const resetTests = () => {
                // 重置方法状态
                const resetStates = {};
                allMethods.forEach(method => {
                    resetStates[method.name] = 'idle';
                });
                setMethodStates(resetStates);

                // 重置统计
                setStats(prev => ({ ...prev, tested: 0, passed: 0, failed: 0 }));

                addLog('🔄 测试状态已重置', 'info');
            };

            const getProviderInfo = (type) => {
                const provider = StorageFactory.create(type);
                return {
                    name: provider.name,
                    icon: provider.icon,
                    type: provider.type
                };
            };

            const getStatusIcon = (status) => {
                const icons = {
                    idle: '⚪',
                    testing: '🔄',
                    success: '✅',
                    error: '❌'
                };
                return icons[status] || '⚪';
            };

            const getCategoryIcon = (category) => {
                const icons = {
                    lifecycle: '🔄',
                    basic: '📁',
                    batch: '📦',
                    metadata: '📊',
                    stream: '🌊',
                    multipart: '🧩',
                    advanced: '⚙️'
                };
                return icons[category] || '🔧';
            };

            const getCategoryTitle = (category) => {
                const titles = {
                    lifecycle: '生命周期管理',
                    basic: '基础CRUD操作',
                    batch: '批量操作',
                    metadata: '元数据操作',
                    stream: '流式操作',
                    multipart: '分块上传',
                    advanced: '高级功能'
                };
                return titles[category] || category;
            };

            return (
                <div className="container">
                    <div className="header">
                        <h1>🧪 存储提供者完整方法测试</h1>
                        <p>全面测试IStorageProvider接口的所有方法实现</p>
                    </div>

                    <div className="main-content">
                        {/* 统计面板 */}
                        <div className="stats-panel">
                            <div className="stat-card">
                                <div className="stat-number">{stats.total}</div>
                                <div className="stat-label">测试方法</div>
                            </div>
                            <div className="stat-card">
                                <div className="stat-number">{stats.tested}</div>
                                <div className="stat-label">已测试</div>
                            </div>
                            <div className="stat-card">
                                <div className="stat-number">{stats.passed}</div>
                                <div className="stat-label">测试通过</div>
                            </div>
                            <div className="stat-card">
                                <div className="stat-number">{stats.failed}</div>
                                <div className="stat-label">测试失败</div>
                            </div>
                        </div>

                        {/* 提供者选择 */}
                        <div className="provider-selector">
                            <h3>📋 选择存储提供者</h3>
                            <div className="provider-grid">
                                {supportedTypes.map(type => {
                                    const info = getProviderInfo(type);
                                    return (
                                        <div
                                            key={type}
                                            className={`provider-card ${selectedProvider === type ? 'active' : ''}`}
                                            onClick={() => selectProvider(type)}
                                        >
                                            <div className="provider-icon">{info.icon}</div>
                                            <div className="provider-name">{info.name}</div>
                                        </div>
                                    );
                                })}
                            </div>
                        </div>

                        {/* 配置面板 */}
                        <div className={`config-panel ${showConfig ? 'show' : ''}`}>
                            <h3>⚙️ 存储提供者配置</h3>

                            {configStatus.show && (
                                <div className={`config-status ${configStatus.type}`}>
                                    {configStatus.message}
                                </div>
                            )}

                            {selectedProvider === StorageType.HUAWEI_OBS && (
                                <>
                                    <div style={{
                                        background: 'rgba(66, 153, 225, 0.1)',
                                        border: '1px solid #4299e1',
                                        borderRadius: '8px',
                                        padding: '15px',
                                        marginBottom: '20px',
                                        color: '#2b6cb0'
                                    }}>
                                        <strong>📝 注意：</strong> 当前为演示版本，使用模拟连接测试。
                                        要使用真实的华为云OBS服务，请根据运行环境选择对应的SDK：
                                        <br/>
                                        <div style={{ marginTop: '8px' }}>
                                            <strong>🌐 浏览器环境/Chrome扩展：</strong>
                                            <br/>
                                            <code style={{ background: 'rgba(0,0,0,0.1)', padding: '2px 6px', borderRadius: '4px', margin: '4px 0', display: 'inline-block' }}>
                                                npm install esdk-obs-browserjs
                                            </code>
                                        </div>
                                        <div style={{ marginTop: '8px' }}>
                                            <strong>🖥️ Node.js服务端：</strong>
                                            <br/>
                                            <code style={{ background: 'rgba(0,0,0,0.1)', padding: '2px 6px', borderRadius: '4px', margin: '4px 0', display: 'inline-block' }}>
                                                npm install esdk-obs-nodejs
                                            </code>
                                        </div>
                                    </div>
                                    <div className="config-form">
                                        <div className="form-group">
                                            <label>Access Key ID *</label>
                                            <input
                                                type="text"
                                                value={configs.huaweiObs.accessKeyId}
                                                onChange={(e) => updateConfig('huaweiObs', 'accessKeyId', e.target.value)}
                                                placeholder="输入华为云Access Key ID"
                                            />
                                        </div>
                                        <div className="form-group">
                                            <label>Secret Access Key *</label>
                                            <input
                                                type="password"
                                                value={configs.huaweiObs.secretAccessKey}
                                                onChange={(e) => updateConfig('huaweiObs', 'secretAccessKey', e.target.value)}
                                                placeholder="输入华为云Secret Access Key"
                                            />
                                        </div>
                                        <div className="form-group">
                                            <label>Endpoint</label>
                                            <input
                                                type="text"
                                                value={configs.huaweiObs.endpoint}
                                                onChange={(e) => updateConfig('huaweiObs', 'endpoint', e.target.value)}
                                                placeholder="OBS服务端点"
                                            />
                                        </div>
                                        <div className="form-group">
                                            <label>Bucket Name *</label>
                                            <input
                                                type="text"
                                                value={configs.huaweiObs.bucketName}
                                                onChange={(e) => updateConfig('huaweiObs', 'bucketName', e.target.value)}
                                                placeholder="存储桶名称"
                                            />
                                        </div>
                                        <div className="form-group">
                                            <label>Region</label>
                                            <select
                                                value={configs.huaweiObs.region}
                                                onChange={(e) => updateConfig('huaweiObs', 'region', e.target.value)}
                                            >
                                                <option value="cn-north-4">华北-北京四(cn-north-4)</option>
                                                <option value="cn-north-1">华北-北京一(cn-north-1)</option>
                                                <option value="cn-east-2">华东-上海二(cn-east-2)</option>
                                                <option value="cn-east-3">华东-上海一(cn-east-3)</option>
                                                <option value="cn-south-1">华南-广州(cn-south-1)</option>
                                            </select>
                                        </div>
                                    </div>
                                </>
                            )}

                            {selectedProvider === StorageType.MINIO && (
                                <>
                                    <div style={{
                                        background: 'rgba(72, 187, 120, 0.1)',
                                        border: '1px solid #48bb78',
                                        borderRadius: '8px',
                                        padding: '15px',
                                        marginBottom: '20px',
                                        color: '#2f855a'
                                    }}>
                                        <strong>📝 注意：</strong> 当前为演示版本，使用模拟连接测试。
                                        要使用真实的MinIO服务，请在项目中引入MinIO SDK：
                                        <br/>
                                        <code style={{ background: 'rgba(0,0,0,0.1)', padding: '2px 6px', borderRadius: '4px', marginTop: '8px', display: 'inline-block' }}>
                                            npm install minio
                                        </code>
                                    </div>
                                    <div className="config-form">
                                        <div className="form-group">
                                            <label>Access Key *</label>
                                            <input
                                                type="text"
                                                value={configs.minio.accessKeyId}
                                                onChange={(e) => updateConfig('minio', 'accessKeyId', e.target.value)}
                                                placeholder="输入MinIO Access Key"
                                            />
                                        </div>
                                        <div className="form-group">
                                            <label>Secret Key *</label>
                                            <input
                                                type="password"
                                                value={configs.minio.secretAccessKey}
                                                onChange={(e) => updateConfig('minio', 'secretAccessKey', e.target.value)}
                                                placeholder="输入MinIO Secret Key"
                                            />
                                        </div>
                                        <div className="form-group">
                                            <label>Endpoint</label>
                                            <input
                                                type="text"
                                                value={configs.minio.endpoint}
                                                onChange={(e) => updateConfig('minio', 'endpoint', e.target.value)}
                                                placeholder="MinIO服务端点"
                                            />
                                        </div>
                                        <div className="form-group">
                                            <label>Bucket Name *</label>
                                            <input
                                                type="text"
                                                value={configs.minio.bucketName}
                                                onChange={(e) => updateConfig('minio', 'bucketName', e.target.value)}
                                                placeholder="存储桶名称"
                                            />
                                        </div>
                                        <div className="form-group">
                                            <label>Region</label>
                                            <input
                                                type="text"
                                                value={configs.minio.region}
                                                onChange={(e) => updateConfig('minio', 'region', e.target.value)}
                                                placeholder="区域名称"
                                            />
                                        </div>
                                        <div className="form-group">
                                            <label>
                                                <input
                                                    type="checkbox"
                                                    checked={configs.minio.useSSL}
                                                    onChange={(e) => updateConfig('minio', 'useSSL', e.target.checked)}
                                                    style={{ marginRight: '8px' }}
                                                />
                                                使用SSL
                                            </label>
                                        </div>
                                    </div>
                                </>
                            )}

                            <div className="config-actions">
                                <button className="config-btn secondary" onClick={cancelConfig}>
                                    取消
                                </button>
                                <button className="config-btn" onClick={saveConfig}>
                                    保存并测试
                                </button>
                            </div>
                        </div>

                        {/* 控制面板 */}
                        <div className="control-panel">
                            <h3>🎛️ 测试控制</h3>
                            <div className="control-buttons">
                                <button
                                    className="control-btn success"
                                    onClick={runAllTests}
                                    disabled={!testRunner}
                                >
                                    🚀 运行所有测试
                                </button>
                                <button
                                    className="control-btn warning"
                                    onClick={clearLogs}
                                >
                                    🧹 清除日志
                                </button>
                                <button
                                    className="control-btn"
                                    onClick={resetTests}
                                >
                                    🔄 重置测试
                                </button>
                                {(selectedProvider === StorageType.HUAWEI_OBS || selectedProvider === StorageType.MINIO) && (
                                    <button
                                        className="control-btn"
                                        onClick={() => setShowConfig(true)}
                                    >
                                        ⚙️ 重新配置
                                    </button>
                                )}
                            </div>
                        </div>

                        {/* 测试方法分组 */}
                        <div className="test-sections">
                            {Object.entries(testMethods).map(([category, methods]) => (
                                <div key={category} className="test-section">
                                    <h4>
                                        {getCategoryIcon(category)} {getCategoryTitle(category)}
                                        <button
                                            className="test-btn"
                                            onClick={() => runCategoryTests(category)}
                                            disabled={!testRunner}
                                            style={{ marginLeft: 'auto' }}
                                        >
                                            测试全部
                                        </button>
                                    </h4>
                                    <div className="test-methods">
                                        {methods.map(method => (
                                            <div key={method.name} className="method-test">
                                                <div className="method-info">
                                                    <div className="method-name">{method.name}</div>
                                                    <div className="method-desc">{method.desc}</div>
                                                </div>
                                                <div className="method-status">
                                                    <div className={`status-indicator status-${methodStates[method.name] || 'idle'}`}></div>
                                                    <span>{getStatusIcon(methodStates[method.name] || 'idle')}</span>
                                                    <button
                                                        className="test-btn"
                                                        onClick={() => runSingleTest(method.name)}
                                                        disabled={!testRunner || methodStates[method.name] === 'testing'}
                                                    >
                                                        测试
                                                    </button>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            ))}
                        </div>

                        {/* 日志终端 */}
                        <div className="log-terminal">
                            <div className="terminal-header">
                                <div className="terminal-dot red"></div>
                                <div className="terminal-dot yellow"></div>
                                <div className="terminal-dot green"></div>
                                <div className="terminal-title">测试日志终端</div>
                            </div>
                            <div className="terminal-content" ref={terminalRef}>
                                {logs.map(log => (
                                    <div key={log.id} className={`log-entry log-${log.type}`}>
                                        [{log.timestamp}] {log.message}
                                    </div>
                                ))}
                                {logs.length === 0 && (
                                    <div className="log-entry log-info">
                                        等待测试日志...
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            );
        }

        // 渲染应用
        ReactDOM.render(<ComprehensiveStorageTest />, document.getElementById('root'));
    </script>
</body>
</html>